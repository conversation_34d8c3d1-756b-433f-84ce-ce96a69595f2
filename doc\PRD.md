## **个人魔术网站 V1.0 - 产品需求文档 (PRD)**

*   **文档版本:** 1.0
*   **发布日期:** 2025年7月28日
*   **产品负责人:** (你的名字)
*   **撰写人:** Gemini (资深产品经理)

### **1. 产品概述与愿景**

#### **1.1. 产品愿景**

打造一个行业领先的个人品牌网站，为有一定基础的魔术师提供一个专业、优雅且富有神秘感的在线平台。本站旨在通过展示世界级的个人作品和深刻的魔术见解，建立行业权威性，并销售独创性高、效果神奇的纸牌魔术教学内容，最终成为进阶魔术爱好者的首选学习资源之一。

#### **1.2. 目标用户**

*   **核心用户:** 有一定魔术基础（熟悉基本手法和理论），希望通过学习独创、高质量的教学内容来提升个人技艺和拓宽魔术视野的进阶魔术师。
*   **用户特征:** 对教学内容辨识度高，追求深度和细节，欣赏优雅的视觉风格，愿意为真正有价值的知识付费。

#### **1.3. 核心价值主张 (Value Proposition)**

*   **专业性 (Professionalism):** 通过精心策划的作品集和博客，展示魔术师的专业水准和深度思考。
*   **独创性 (Originality):** 提供市面上稀缺的原创魔术教学，专注于方法和效果的创新。
*   **优质体验 (Premium Experience):** 提供从浏览、购买到学习的全流程无缝、流畅的高端用户体验。

### **2. 用户故事 (User Stories)**

*   **作为一名访客,** 我希望能快速浏览魔术师最精彩的作品，以便判断他的水平是否值得我关注。
*   **作为一名访客,** 我希望能阅读一些深度的文章，以便了解这位魔术师的思考方式和艺术理念。
*   **作为一名潜在买家,** 我希望在产品介绍页看到足够详尽的图文描述，以便完全理解我将要购买的内容价值。
*   **作为一名首次购买者,** 我希望购买流程尽可能简单，最好能在支付的同时就自动帮我创建好账户，无需单独的注册步骤。
*   **作为一名已购买用户,** 我希望能有一个清晰的个人中心，方便地找到我所有购买过的教学视频。
*   **作为一名学习者,** 我希望在一个教学课程中，可以清晰地看到所有包含的视频片段列表，并能自由切换观看。

### **3. 功能需求 V1.0 (MVP)**

#### **3.1. 公共页面模块**

| **功能模块** | **详细需求描述** | **UI/UX 关键点** |
| :--- | :--- | :--- |
| **3.1.1. 首页 (Homepage)** | - **布局:** 采用策展式布局，而非简单的链接列表。<br>- **内容:** 依次包含：1) 强视觉冲击力的主视觉Hero区（图片或短视频）；2) 精选的代表性作品展示入口；3) 最新或最受欢迎的博客文章摘要入口；4) 指向所有教学产品的明确行动号召（Call-to-Action）按钮。<br>- **导航:** 顶部需要有清晰的全局导航栏（如：作品、博客、教学、登录）。 | - 整体风格：神秘、优雅、极简。<br>- 动效：可使用平滑的滚动视差或渐入效果，增强高级感。<br>- 响应式设计：确保在桌面和移动设备上都有完美的浏览体验。 |
| **3.1.2. 作品集页 (Portfolio)** | - 以网格或列表形式，展示个人魔术表演的视频或图片。<br>- 每个作品应包含标题和简短描述。<br>- 点击作品可弹出播放器或跳转至详情页观看。 | - 视觉优先，让作品本身说话。<br>- 加载速度要快，视频封面优化。 |
| **3.1.3. 博客页 (Blog)** | - 以文章列表形式展示所有博客文章。<br>- 每篇文章显示标题、发布日期和摘要。<br>- 点击可进入文章详情页，阅读全文。 | -  typography（字体排印）至关重要，保证长文阅读的舒适性。 |
| **3.1.4. 教学列表页 (Tutorials)** | - 以卡片形式展示所有可供销售的教学产品。<br>- 每个卡片包含产品封面、标题和价格。<br>- 点击卡片进入该产品的详情介绍页。 | - 布局清晰，方便用户快速浏览和比较。 |

#### **3.2. 产品与购买模块**

| **功能模块** | **详细需求描述** | **UI/UX 关键点** |
| :--- | :--- | :--- |
| **3.2.1. 产品详情页** | - **核心要求:** 产品描述区域必须支持 **Markdown** 渲染。这允许管理员（魔术师本人）自由编辑图文混排的深度内容。<br>- **页面结构:** 包含产品标题、价格、**购买按钮**、以及由Markdown生成的详细介绍。<br>- **媒体:** 允许在Markdown中嵌入图片，以辅助说明。 | - “购买”按钮必须非常醒目。<br>- 页面设计需给予Markdown内容足够的展示空间和优雅的排版。 |
| **3.2.2. 购买与账户创建流程** | - **无购物车模式:** 用户点击“购买”按钮后，直接触发支付流程。<br>- **支付集成:** 与 **PayPal** API 集成。<br>- **无缝注册:** 1. 用户点击购买，弹出PayPal支付窗口。 2. 用户在PayPal完成支付。 3. 系统接收到PayPal成功支付的回调通知。 4. 系统使用用户的PayPal邮箱进行判断：a) 若该邮箱的账户已存在，则直接为该账户授权。b) 若账户不存在，则系统自动为该邮箱创建一个新账户，并将购买的教学授权给此新账户。 5. 流程结束后，用户被自动重定向到“我的教学库”页面。 | - 这是整个体验的核心，必须做到极致顺滑。<br>- 需要给用户明确的提示，例如支付成功后显示“购买成功！正在为您跳转至教学库...”。<br>- 首次购买的用户可能会收到一封邮件，告知他们账户已创建，并提供设置密码的链接（可选，但推荐）。 |

#### **3.3. 用户后台模块 (需登录访问)**

| **功能模块** | **详细需求描述** | **UI/UX 关键点** |
| :--- | :--- | :--- |
| **3.3.1. 用户认证** | - 提供邮箱/密码登录功能。<br>- 首次通过购买创建账户的用户，可通过“忘记密码”功能来设置初始密码。 | - 登录/注册页面风格需与全站保持一致。 |
| **3.3.2. 我的教学库 (My Library)** | - 此页面展示用户已购买的所有教学产品的卡片列表。<br>- 只有已购买并授权的产品才会在此处显示。<br>- 点击任一产品卡片，即可进入对应的视频观看页。 | - 设计应像一个私人的、整洁的书架。 |
| **3.3.3. 视频观看页 (Watch Page)** | - **布局:** 采用“课程”式布局。页面主体是视频播放器，侧边（左或右）是该教学包含的**视频列表（播放列表）**。<br>- **功能:** a) 一个教学可能包含一个或多个视频，在播放列表中清晰列出。b) 点击列表中的视频，右侧播放器会切换到对应视频。c) 播放器集成 **Mux Player**，保证高质量、安全的流式播放。 | - 播放列表需要突出显示当前正在播放的视频。<br>- 页面设计应无干扰，让用户能沉浸在学习中。 |

### **4. 非功能性需求**

*   **技术栈:**
    *   **前端:** Next.js (React)
    *   **CSS:** Tailwind CSS
    *   **部署/无服务器函数:** Vercel
    *   **视频托管:** Mux
    *   **支付网关:** PayPal
    *   **数据库:** Supabase
    *   **用户认证:** Supabase
*   **性能:** 网站加载速度必须快。公共页面（首页、博客、作品集）应尽可能使用静态生成（SSG）以获得最佳性能和SEO。
*   **安全:** 用户数据和密码需使用Supabase。视频流需使用Mux提供的DRM安全机制，防止盗录。
*   **布局:** 兼容PC/移动端布局。
