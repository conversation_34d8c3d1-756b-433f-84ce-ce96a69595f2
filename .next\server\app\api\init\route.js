/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/init/route";
exports.ids = ["app/api/init/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Finit%2Froute&page=%2Fapi%2Finit%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finit%2Froute.ts&appDir=C%3A%5Ccode%5Caug%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Ccode%5Caug&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Finit%2Froute&page=%2Fapi%2Finit%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finit%2Froute.ts&appDir=C%3A%5Ccode%5Caug%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Ccode%5Caug&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var C_code_aug_src_app_api_init_route_ts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./src/app/api/init/route.ts */ \"(rsc)/./src/app/api/init/route.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/init/route\",\n        pathname: \"/api/init\",\n        filename: \"route\",\n        bundlePath: \"app/api/init/route\"\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || '',\n    resolvedPagePath: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\api\\\\init\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_code_aug_src_app_api_init_route_ts__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/init/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Finit%2Froute&page=%2Fapi%2Finit%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finit%2Froute.ts&appDir=C%3A%5Ccode%5Caug%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Ccode%5Caug&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/init/route.ts":
/*!***********************************!*\
  !*** ./src/app/api/init/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_seed_data__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/seed-data */ \"(rsc)/./src/lib/seed-data.ts\");\n\n\nasync function POST() {\n    try {\n        await (0,_lib_seed_data__WEBPACK_IMPORTED_MODULE_1__.seedDatabase)();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Database initialized and seeded successfully'\n        });\n    } catch (error) {\n        console.error('Database initialization error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to initialize database',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET() {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        message: 'Database initialization endpoint. Use POST to initialize.',\n        endpoints: {\n            'POST /api/init': 'Initialize and seed the database'\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/init/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PurchaseModel: () => (/* binding */ PurchaseModel),\n/* harmony export */   TutorialModel: () => (/* binding */ TutorialModel),\n/* harmony export */   UserModel: () => (/* binding */ UserModel),\n/* harmony export */   initializeDatabase: () => (/* binding */ initializeDatabase)\n/* harmony export */ });\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs/promises */ \"fs/promises\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs_promises__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/uuid/dist/esm/v4.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n\n\n\n\n// TODO: Replace with SQLite/Supabase integration\n// This is a simple JSON-based database for local development\n// In production, this would be replaced with:\n// - SQLite for local development\n// - Supabase for production (as specified in PRD)\nconst DB_PATH = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'data');\nconst USERS_FILE = path__WEBPACK_IMPORTED_MODULE_1___default().join(DB_PATH, 'users.json');\nconst TUTORIALS_FILE = path__WEBPACK_IMPORTED_MODULE_1___default().join(DB_PATH, 'tutorials.json');\nconst VIDEOS_FILE = path__WEBPACK_IMPORTED_MODULE_1___default().join(DB_PATH, 'videos.json');\nconst PURCHASES_FILE = path__WEBPACK_IMPORTED_MODULE_1___default().join(DB_PATH, 'purchases.json');\nconst BLOG_POSTS_FILE = path__WEBPACK_IMPORTED_MODULE_1___default().join(DB_PATH, 'blog_posts.json');\nconst PORTFOLIO_FILE = path__WEBPACK_IMPORTED_MODULE_1___default().join(DB_PATH, 'portfolio.json');\n// Initialize database files\nasync function initializeDatabase() {\n    try {\n        await fs_promises__WEBPACK_IMPORTED_MODULE_0___default().mkdir(DB_PATH, {\n            recursive: true\n        });\n        // Initialize empty files if they don't exist\n        const files = [\n            {\n                path: USERS_FILE,\n                data: []\n            },\n            {\n                path: TUTORIALS_FILE,\n                data: []\n            },\n            {\n                path: VIDEOS_FILE,\n                data: []\n            },\n            {\n                path: PURCHASES_FILE,\n                data: []\n            },\n            {\n                path: BLOG_POSTS_FILE,\n                data: []\n            },\n            {\n                path: PORTFOLIO_FILE,\n                data: []\n            }\n        ];\n        for (const file of files){\n            try {\n                await fs_promises__WEBPACK_IMPORTED_MODULE_0___default().access(file.path);\n            } catch  {\n                await fs_promises__WEBPACK_IMPORTED_MODULE_0___default().writeFile(file.path, JSON.stringify(file.data, null, 2));\n            }\n        }\n        console.log('Database initialized successfully');\n    } catch (error) {\n        console.error('Failed to initialize database:', error);\n        throw error;\n    }\n}\n// Generic file operations\nasync function readJsonFile(filePath) {\n    try {\n        const data = await fs_promises__WEBPACK_IMPORTED_MODULE_0___default().readFile(filePath, 'utf-8');\n        return JSON.parse(data);\n    } catch (error) {\n        console.error(`Error reading ${filePath}:`, error);\n        return [];\n    }\n}\nasync function writeJsonFile(filePath, data) {\n    try {\n        await fs_promises__WEBPACK_IMPORTED_MODULE_0___default().writeFile(filePath, JSON.stringify(data, null, 2));\n    } catch (error) {\n        console.error(`Error writing ${filePath}:`, error);\n        throw error;\n    }\n}\n// User operations\nclass UserModel {\n    static async create(userData) {\n        const users = await readJsonFile(USERS_FILE);\n        // Check if user already exists\n        const existingUser = users.find((u)=>u.email === userData.email);\n        if (existingUser) {\n            throw new Error('User already exists');\n        }\n        const hashedPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_2__[\"default\"].hash(userData.password, 12);\n        const now = new Date().toISOString();\n        const newUser = {\n            id: (0,uuid__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(),\n            email: userData.email,\n            password_hash: hashedPassword,\n            name: userData.name || null,\n            created_at: now,\n            updated_at: now\n        };\n        users.push(newUser);\n        await writeJsonFile(USERS_FILE, users);\n        return {\n            id: newUser.id,\n            email: newUser.email,\n            name: newUser.name || undefined,\n            createdAt: new Date(newUser.created_at),\n            updatedAt: new Date(newUser.updated_at)\n        };\n    }\n    static async findByEmail(email) {\n        const users = await readJsonFile(USERS_FILE);\n        const user = users.find((u)=>u.email === email);\n        if (!user) return null;\n        return {\n            id: user.id,\n            email: user.email,\n            name: user.name || undefined,\n            passwordHash: user.password_hash,\n            createdAt: new Date(user.created_at),\n            updatedAt: new Date(user.updated_at)\n        };\n    }\n    static async findById(id) {\n        const users = await readJsonFile(USERS_FILE);\n        const user = users.find((u)=>u.id === id);\n        if (!user) return null;\n        return {\n            id: user.id,\n            email: user.email,\n            name: user.name || undefined,\n            createdAt: new Date(user.created_at),\n            updatedAt: new Date(user.updated_at)\n        };\n    }\n    static async verifyPassword(email, password) {\n        const user = await this.findByEmail(email);\n        if (!user) return null;\n        const isValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_2__[\"default\"].compare(password, user.passwordHash);\n        if (!isValid) return null;\n        return {\n            id: user.id,\n            email: user.email,\n            name: user.name,\n            createdAt: user.createdAt,\n            updatedAt: user.updatedAt\n        };\n    }\n}\n// Tutorial operations\nclass TutorialModel {\n    static async create(tutorialData) {\n        const tutorials = await readJsonFile(TUTORIALS_FILE);\n        const now = new Date().toISOString();\n        const newTutorial = {\n            id: (0,uuid__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(),\n            title: tutorialData.title,\n            description: tutorialData.description,\n            price: tutorialData.price,\n            cover_image: tutorialData.coverImage || null,\n            created_at: now,\n            updated_at: now\n        };\n        tutorials.push(newTutorial);\n        await writeJsonFile(TUTORIALS_FILE, tutorials);\n        return {\n            id: newTutorial.id,\n            title: newTutorial.title,\n            description: newTutorial.description,\n            price: newTutorial.price,\n            coverImage: newTutorial.cover_image || undefined,\n            videos: [],\n            createdAt: new Date(newTutorial.created_at),\n            updatedAt: new Date(newTutorial.updated_at)\n        };\n    }\n    static async findAll() {\n        const tutorials = await readJsonFile(TUTORIALS_FILE);\n        const videos = await readJsonFile(VIDEOS_FILE);\n        return tutorials.map((tutorial)=>({\n                id: tutorial.id,\n                title: tutorial.title,\n                description: tutorial.description,\n                price: tutorial.price,\n                coverImage: tutorial.cover_image || undefined,\n                videos: videos.filter((v)=>v.tutorial_id === tutorial.id).sort((a, b)=>a.order_index - b.order_index).map((v)=>({\n                        id: v.id,\n                        title: v.title,\n                        description: v.description || undefined,\n                        duration: v.duration,\n                        videoUrl: v.video_url,\n                        thumbnailUrl: v.thumbnail_url || undefined,\n                        order: v.order_index,\n                        tutorialId: v.tutorial_id\n                    })),\n                createdAt: new Date(tutorial.created_at),\n                updatedAt: new Date(tutorial.updated_at)\n            }));\n    }\n    static async findById(id) {\n        const tutorials = await this.findAll();\n        return tutorials.find((t)=>t.id === id) || null;\n    }\n}\n// Purchase operations\nclass PurchaseModel {\n    static async create(purchaseData) {\n        const purchases = await readJsonFile(PURCHASES_FILE);\n        const now = new Date().toISOString();\n        const newPurchase = {\n            id: (0,uuid__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(),\n            user_id: purchaseData.userId,\n            tutorial_id: purchaseData.tutorialId,\n            amount: purchaseData.amount,\n            payment_id: purchaseData.paymentId,\n            status: 'completed',\n            created_at: now\n        };\n        purchases.push(newPurchase);\n        await writeJsonFile(PURCHASES_FILE, purchases);\n        return {\n            id: newPurchase.id,\n            userId: newPurchase.user_id,\n            tutorialId: newPurchase.tutorial_id,\n            amount: newPurchase.amount,\n            paymentId: newPurchase.payment_id,\n            status: 'completed',\n            createdAt: new Date(newPurchase.created_at)\n        };\n    }\n    static async findByUserId(userId) {\n        const purchases = await readJsonFile(PURCHASES_FILE);\n        return purchases.filter((p)=>p.user_id === userId).map((p)=>({\n                id: p.id,\n                userId: p.user_id,\n                tutorialId: p.tutorial_id,\n                amount: p.amount,\n                paymentId: p.payment_id,\n                status: p.status,\n                createdAt: new Date(p.created_at)\n            }));\n    }\n    static async hasUserPurchased(userId, tutorialId) {\n        const purchases = await readJsonFile(PURCHASES_FILE);\n        return purchases.some((p)=>p.user_id === userId && p.tutorial_id === tutorialId && p.status === 'completed');\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/seed-data.ts":
/*!******************************!*\
  !*** ./src/lib/seed-data.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   sampleBlogPosts: () => (/* binding */ sampleBlogPosts),\n/* harmony export */   samplePortfolio: () => (/* binding */ samplePortfolio),\n/* harmony export */   seedDatabase: () => (/* binding */ seedDatabase)\n/* harmony export */ });\n/* harmony import */ var _database__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./database */ \"(rsc)/./src/lib/database.ts\");\n\nasync function seedDatabase() {\n    try {\n        await (0,_database__WEBPACK_IMPORTED_MODULE_0__.initializeDatabase)();\n        // Create sample tutorials\n        const tutorials = [\n            {\n                title: \"Advanced Card Control Techniques\",\n                description: \"Master the art of controlling cards with precision and elegance. This comprehensive tutorial covers advanced methods for maintaining control of selected cards throughout complex routines.\",\n                price: 29.99,\n                coverImage: \"/images/tutorials/card-control.jpg\"\n            },\n            {\n                title: \"Invisible Deck Mastery\",\n                description: \"Learn the secrets behind one of magic's most powerful tools. From basic handling to advanced presentations, this tutorial will transform your invisible deck work.\",\n                price: 39.99,\n                coverImage: \"/images/tutorials/invisible-deck.jpg\"\n            },\n            {\n                title: \"Psychological Forces and Mentalism\",\n                description: \"Dive deep into the psychology of magic and learn how to influence spectator choices without them knowing. Essential techniques for any serious mentalist.\",\n                price: 49.99,\n                coverImage: \"/images/tutorials/mentalism.jpg\"\n            }\n        ];\n        for (const tutorial of tutorials){\n            try {\n                await _database__WEBPACK_IMPORTED_MODULE_0__.TutorialModel.create(tutorial);\n                console.log(`Created tutorial: ${tutorial.title}`);\n            } catch (error) {\n                console.log(`Tutorial already exists: ${tutorial.title}`);\n            }\n        }\n        console.log('Database seeded successfully');\n    } catch (error) {\n        console.error('Error seeding database:', error);\n    }\n}\n// Sample blog posts data\nconst sampleBlogPosts = [\n    {\n        title: \"The Philosophy of Magic: Art vs Entertainment\",\n        excerpt: \"Exploring the eternal debate between magic as pure art form and magic as entertainment medium.\",\n        content: `# The Philosophy of Magic: Art vs Entertainment\n\nMagic has always existed at the intersection of art and entertainment, creating an ongoing debate about its true nature. As practitioners, we must ask ourselves: Are we artists expressing our creativity, or entertainers providing momentary wonder?\n\n## The Artistic Perspective\n\nWhen we view magic as art, we focus on:\n- **Expression**: Each effect becomes a canvas for personal creativity\n- **Meaning**: Tricks transform into metaphors and statements\n- **Technique**: Method becomes secondary to artistic vision\n- **Audience**: Spectators become participants in an artistic experience\n\n## The Entertainment Perspective\n\nWhen we prioritize entertainment, we emphasize:\n- **Engagement**: Keeping the audience captivated and involved\n- **Accessibility**: Making magic understandable and relatable\n- **Impact**: Creating memorable moments and reactions\n- **Commercial viability**: Ensuring our work has market appeal\n\n## Finding Balance\n\nThe most successful magicians often find a balance between these perspectives. They understand that:\n\n1. **Art without entertainment** can become self-indulgent and alienating\n2. **Entertainment without artistry** can become shallow and forgettable\n3. **True mastery** comes from serving both the art and the audience\n\n## Conclusion\n\nPerhaps the question isn't whether magic is art or entertainment, but how we can honor both aspects in our work. The greatest magic happens when artistic vision meets entertainment value, creating experiences that are both meaningful and memorable.`,\n        slug: \"philosophy-of-magic-art-vs-entertainment\",\n        publishedAt: new Date('2025-01-15')\n    },\n    {\n        title: \"The Psychology of Misdirection\",\n        excerpt: \"Understanding how attention works and how magicians can ethically guide it.\",\n        content: `# The Psychology of Misdirection\n\nMisdirection is often misunderstood as simply \"looking away,\" but it's actually a sophisticated understanding of human psychology and attention.\n\n## Types of Attention\n\n### Overt Attention\nThis is conscious, directed attention - where the spectator knows they're looking.\n\n### Covert Attention\nThis is unconscious attention - the mind's automatic processing of information.\n\n## Principles of Misdirection\n\n1. **Natural vs Unnatural**: People notice what seems out of place\n2. **Movement**: The eye follows motion\n3. **Contrast**: Sudden changes draw attention\n4. **Interest**: People look at what interests them\n5. **Expectation**: We see what we expect to see\n\n## Ethical Considerations\n\nAs magicians, we have a responsibility to use misdirection ethically:\n- Respect your audience's intelligence\n- Create wonder, not confusion\n- Use psychology to enhance, not manipulate\n\nThe goal is to create a collaborative experience where the audience willingly suspends disbelief.`,\n        slug: \"psychology-of-misdirection\",\n        publishedAt: new Date('2025-01-10')\n    }\n];\n// Sample portfolio items\nconst samplePortfolio = [\n    {\n        title: \"Ethereal Card Manipulation\",\n        description: \"A mesmerizing display of card manipulation that defies the laws of physics.\",\n        mediaUrl: \"/videos/portfolio/ethereal-cards.mp4\",\n        mediaType: \"video\",\n        order: 1\n    },\n    {\n        title: \"Mind Reading Demonstration\",\n        description: \"An impossible demonstration of mind reading with a borrowed deck of cards.\",\n        mediaUrl: \"/videos/portfolio/mind-reading.mp4\",\n        mediaType: \"video\",\n        order: 2\n    },\n    {\n        title: \"Levitation Performance\",\n        description: \"Objects float and dance in mid-air in this stunning levitation routine.\",\n        mediaUrl: \"/images/portfolio/levitation.jpg\",\n        mediaType: \"image\",\n        order: 3\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/seed-data.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "./work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "fs/promises":
/*!******************************!*\
  !*** external "fs/promises" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("fs/promises");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/uuid","vendor-chunks/bcryptjs"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Finit%2Froute&page=%2Fapi%2Finit%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finit%2Froute.ts&appDir=C%3A%5Ccode%5Caug%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Ccode%5Caug&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();