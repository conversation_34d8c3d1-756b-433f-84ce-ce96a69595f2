{"name": "magic-website", "version": "1.0.0", "private": true, "description": "Personal Magic Website - A professional platform for magic tutorials and content", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "keywords": ["magic", "tutorials", "nextjs", "education"], "author": "", "license": "ISC", "dependencies": {"@tailwindcss/postcss": "^4.1.11", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^24.1.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.21", "bcryptjs": "^3.0.2", "clsx": "^2.1.1", "eslint": "^9.32.0", "eslint-config-next": "^15.4.4", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.526.0", "next": "^15.4.4", "postcss": "^8.5.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-markdown": "^10.1.0", "tailwindcss": "^4.1.11", "typescript": "^5.8.3", "uuid": "^11.1.0"}}